<link rel="stylesheet" href="/src/assets/bpmn-js/diagram-js.css" />
<link rel="stylesheet" href="/src/assets/bpmn-js/bpmn-font/css/bpmn.css" />

<div class="with-icon">
  <h3 id="page-headline">{{ 'MENU.WORKFLOW' | translate }}</h3>

  <div class="workflow-controls">
    <mat-form-field appearance="outline" class="workflow-type-selector">
      <mat-label>{{ 'WORKFLOW.TYPE' | translate }}</mat-label>
      <mat-select [(value)]="selectedWorkflowType" (selectionChange)="onWorkflowTypeChange()">
        @for (type of workflowTypes; track type.value) {
          <mat-option [value]="type.value">{{ type.label | translate }}</mat-option>
        }
      </mat-select>
    </mat-form-field>

    @if (!isLoading) {
      <button mat-icon-button (click)="zoomIn()">
        <mat-icon>zoom_in</mat-icon>
      </button>
      <button mat-icon-button (click)="zoomOut()">
        <mat-icon>zoom_out</mat-icon>
      </button>
      <button mat-icon-button (click)="back()">
        <mat-icon>home_pin</mat-icon>
      </button>
    }
  </div>
</div>

@if (isLoading) {
  <mat-spinner></mat-spinner>
}

<div id="workflow-canvas"></div>
